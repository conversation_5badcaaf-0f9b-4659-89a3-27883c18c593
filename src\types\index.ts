
export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'progress' | 'done';
  dueDate?: string;
  priority?: 'low' | 'medium' | 'high';
  link?: string;
  tags?: string[];
  attachedImage?: string;
  pageId?: string;
  order: number;
  createdAt: string;
}

export interface Page {
  id: string;
  title: string;
  description: string;
  category: string;
  url?: string;
  color: string;
  createdAt: string;
  tasks: Task[];
}

export interface AppState {
  pages: Page[];
  unassignedTasks: Task[];
}

export const TASK_STATUSES = {
  todo: { label: 'To Do', color: 'bg-task-todo', textColor: 'text-blue-700' },
  progress: { label: 'In Progress', color: 'bg-task-progress', textColor: 'text-yellow-700' },
  done: { label: 'Done', color: 'bg-task-done', textColor: 'text-green-700' }
} as const;

export const PAGE_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', 
  '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
];

export const CATEGORIES = [
  'Work', 'Personal', 'Shopping', 'Health', 'Education', 
  'Entertainment', 'Finance', 'Travel', 'Home', 'Other'
];
