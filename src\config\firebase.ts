
// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyC_TUtoeW9W3-hPPrSngG8YLY9-CY9SnaE",
  authDomain: "tasker-8d9e0.firebaseapp.com",
  projectId: "tasker-8d9e0",
  storageBucket: "tasker-8d9e0.firebasestorage.app",
  messagingSenderId: "325664032304",
  appId: "1:325664032304:web:62e7ef52be93db7b938ff5",
  measurementId: "G-JVW5P7TLQ5"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Analytics only if in browser environment and not in development
let analytics;
if (typeof window !== 'undefined' && !import.meta.env.DEV) {
  try {
    analytics = getAnalytics(app);
  } catch (error) {
    console.warn('Analytics initialization failed:', error);
  }
}

export const auth = getAuth(app);
export const db = getFirestore(app);
export const googleProvider = new GoogleAuthProvider();

// Configure Google provider for better UX
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

export { analytics };
export default app;
