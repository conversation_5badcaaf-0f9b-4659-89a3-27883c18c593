
import { AppState, Page, Task } from '../types';

const STORAGE_KEY_PREFIX = 'taskerlister-data';

export const loadFromStorage = (userId?: string): AppState => {
  try {
    if (typeof window === 'undefined') {
      console.warn('localStorage not available');
      return { pages: [], unassignedTasks: [] };
    }

    const storageKey = userId ? `${STORAGE_KEY_PREFIX}-${userId}` : STORAGE_KEY_PREFIX;
    console.log('Loading data from localStorage with key:', storageKey);
    
    const data = localStorage.getItem(storageKey);
    if (data) {
      const parsedData = JSON.parse(data);
      console.log('Loaded data from localStorage:', parsedData);
      
      // Validate the data structure
      if (parsedData && typeof parsedData === 'object' && 
          Array.isArray(parsedData.pages) && Array.isArray(parsedData.unassignedTasks)) {
        return parsedData;
      } else {
        console.warn('Invalid data structure in localStorage, returning default state');
        return { pages: [], unassignedTasks: [] };
      }
    }
  } catch (error) {
    console.error('Error loading from localStorage:', error);
  }
  
  console.log('Returning default state');
  return {
    pages: [],
    unassignedTasks: []
  };
};

export const saveToStorage = (state: AppState, userId?: string): void => {
  try {
    if (typeof window === 'undefined') {
      console.warn('localStorage not available');
      return;
    }

    const storageKey = userId ? `${STORAGE_KEY_PREFIX}-${userId}` : STORAGE_KEY_PREFIX;
    console.log('Saving data to localStorage with key:', storageKey, state);
    
    // Validate state before saving
    if (state && typeof state === 'object' && 
        Array.isArray(state.pages) && Array.isArray(state.unassignedTasks)) {
      localStorage.setItem(storageKey, JSON.stringify(state));
      console.log('Data saved successfully to localStorage');
    } else {
      console.error('Invalid state structure, not saving to localStorage:', state);
    }
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Utility function to clear storage for a specific user
export const clearUserStorage = (userId: string): void => {
  try {
    if (typeof window === 'undefined') return;
    
    const storageKey = `${STORAGE_KEY_PREFIX}-${userId}`;
    localStorage.removeItem(storageKey);
    console.log('Cleared storage for user:', userId);
  } catch (error) {
    console.error('Error clearing user storage:', error);
  }
};
