import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AppState, Task, Page } from '../types';
import { loadFromStorage, saveToStorage, generateId } from '../utils/localStorage';
import { useAuth } from './SupabaseAuthContext';

interface TaskContextType {
  state: AppState;
  addTask: (task: Omit<Task, 'id' | 'createdAt' | 'order'>) => void;
  updateTask: (taskId: string, updates: Partial<Task>) => void;
  deleteTask: (taskId: string) => void;
  duplicateTask: (taskId: string, targetPageId?: string) => void;
  addPage: (page: Omit<Page, 'id' | 'createdAt' | 'tasks'>) => void;
  updatePage: (pageId: string, updates: Partial<Page>) => void;
  deletePage: (pageId: string) => void;
  moveTask: (taskId: string, targetPageId?: string, targetIndex?: number) => void;
  searchTasks: (query: string) => Task[];
}

const TaskContext = createContext<TaskContextType | undefined>(undefined);

type Action =
  | { type: 'LOAD_STATE'; payload: AppState }
  | { type: 'ADD_TASK'; payload: Task }
  | { type: 'UPDATE_TASK'; payload: { taskId: string; updates: Partial<Task> } }
  | { type: 'DELETE_TASK'; payload: string }
  | { type: 'DUPLICATE_TASK'; payload: { originalTaskId: string; targetPageId?: string } }
  | { type: 'ADD_PAGE'; payload: Page }
  | { type: 'UPDATE_PAGE'; payload: { pageId: string; updates: Partial<Page> } }
  | { type: 'DELETE_PAGE'; payload: string }
  | { type: 'MOVE_TASK'; payload: { taskId: string; targetPageId?: string; targetIndex?: number } };

const taskReducer = (state: AppState, action: Action): AppState => {
  switch (action.type) {
    case 'LOAD_STATE':
      return action.payload;

    case 'ADD_TASK': {
      return {
        ...state,
        unassignedTasks: [...state.unassignedTasks, action.payload]
      };
    }

    case 'UPDATE_TASK': {
      const { taskId, updates } = action.payload;
      
      const updateTaskInList = (tasks: Task[]) =>
        tasks.map(task => task.id === taskId ? { ...task, ...updates } : task);

      return {
        ...state,
        unassignedTasks: updateTaskInList(state.unassignedTasks),
        pages: state.pages.map(page => ({
          ...page,
          tasks: updateTaskInList(page.tasks)
        }))
      };
    }

    case 'DELETE_TASK': {
      const taskId = action.payload;
      
      return {
        ...state,
        unassignedTasks: state.unassignedTasks.filter(task => task.id !== taskId),
        pages: state.pages.map(page => ({
          ...page,
          tasks: page.tasks.filter(task => task.id !== taskId)
        }))
      };
    }

    case 'DUPLICATE_TASK': {
      const { originalTaskId, targetPageId } = action.payload;
      
      // Find the original task
      let originalTask: Task | undefined;
      
      // Check unassigned tasks
      originalTask = state.unassignedTasks.find(task => task.id === originalTaskId);
      
      if (!originalTask) {
        // Check page tasks
        for (const page of state.pages) {
          originalTask = page.tasks.find(task => task.id === originalTaskId);
          if (originalTask) break;
        }
      }
      
      if (!originalTask) return state;
      
      // Create duplicated task
      const duplicatedTask: Task = {
        ...originalTask,
        id: generateId(),
        title: `${originalTask.title} (Copy)`,
        createdAt: new Date().toISOString(),
        pageId: targetPageId,
        order: 0
      };
      
      if (targetPageId) {
        // Add to specific page
        const targetPageIndex = state.pages.findIndex(page => page.id === targetPageId);
        if (targetPageIndex >= 0) {
          const updatedPages = [...state.pages];
          updatedPages[targetPageIndex] = {
            ...updatedPages[targetPageIndex],
            tasks: [duplicatedTask, ...updatedPages[targetPageIndex].tasks].map((task, index) => ({ ...task, order: index }))
          };
          
          return {
            ...state,
            pages: updatedPages
          };
        }
      } else {
        // Add to unassigned tasks
        return {
          ...state,
          unassignedTasks: [duplicatedTask, ...state.unassignedTasks].map((task, index) => ({ ...task, order: index }))
        };
      }
      
      return state;
    }

    case 'ADD_PAGE': {
      return {
        ...state,
        pages: [...state.pages, action.payload]
      };
    }

    case 'UPDATE_PAGE': {
      const { pageId, updates } = action.payload;
      return {
        ...state,
        pages: state.pages.map(page => 
          page.id === pageId ? { ...page, ...updates } : page
        )
      };
    }

    case 'DELETE_PAGE': {
      const pageId = action.payload;
      const pageToDelete = state.pages.find(page => page.id === pageId);
      
      if (!pageToDelete) return state;
      
      // Move tasks back to unassigned
      const tasksToMove = pageToDelete.tasks.map(task => ({
        ...task,
        pageId: undefined,
        order: state.unassignedTasks.length + pageToDelete.tasks.indexOf(task)
      }));
      
      return {
        ...state,
        unassignedTasks: [...state.unassignedTasks, ...tasksToMove],
        pages: state.pages.filter(page => page.id !== pageId)
      };
    }

    case 'MOVE_TASK': {
      const { taskId, targetPageId, targetIndex } = action.payload;
      
      // Find the task and its current location
      let sourceTask: Task | undefined;
      let sourcePageId: string | undefined;
      
      // Check unassigned tasks
      const unassignedTask = state.unassignedTasks.find(task => task.id === taskId);
      if (unassignedTask) {
        sourceTask = unassignedTask;
      } else {
        // Check page tasks
        for (const page of state.pages) {
          const pageTask = page.tasks.find(task => task.id === taskId);
          if (pageTask) {
            sourceTask = pageTask;
            sourcePageId = page.id;
            break;
          }
        }
      }
      
      if (!sourceTask) return state;
      
      // Remove task from source
      const newUnassignedTasks = state.unassignedTasks.filter(task => task.id !== taskId);
      const newPages = state.pages.map(page => ({
        ...page,
        tasks: page.tasks.filter(task => task.id !== taskId)
      }));
      
      // Add task to target
      const updatedTask = { ...sourceTask, pageId: targetPageId };
      
      if (targetPageId) {
        // Moving to a page
        const targetPageIndex = newPages.findIndex(page => page.id === targetPageId);
        if (targetPageIndex >= 0) {
          const targetPage = newPages[targetPageIndex];
          const newTasks = [...targetPage.tasks];
          const insertIndex = targetIndex !== undefined ? targetIndex : newTasks.length;
          newTasks.splice(insertIndex, 0, updatedTask);
          
          newPages[targetPageIndex] = {
            ...targetPage,
            tasks: newTasks.map((task, index) => ({ ...task, order: index }))
          };
        }
      } else {
        // Moving to unassigned
        const insertIndex = targetIndex !== undefined ? targetIndex : newUnassignedTasks.length;
        newUnassignedTasks.splice(insertIndex, 0, { ...updatedTask, pageId: undefined });
      }
      
      return {
        ...state,
        unassignedTasks: newUnassignedTasks.map((task, index) => ({ ...task, order: index })),
        pages: newPages
      };
    }

    default:
      return state;
  }
};

export const TaskProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(taskReducer, { pages: [], unassignedTasks: [] });
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      const loadedState = loadFromStorage(user.uid);
      dispatch({ type: 'LOAD_STATE', payload: loadedState });
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      saveToStorage(state, user.uid);
    }
  }, [state, user]);

  const addTask = (taskData: Omit<Task, 'id' | 'createdAt' | 'order'>) => {
    const newTask: Task = {
      ...taskData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      order: state.unassignedTasks.length,
      priority: taskData.priority || 'medium',
      tags: taskData.tags || [],
    };
    dispatch({ type: 'ADD_TASK', payload: newTask });
  };

  const updateTask = (taskId: string, updates: Partial<Task>) => {
    dispatch({ type: 'UPDATE_TASK', payload: { taskId, updates } });
  };

  const deleteTask = (taskId: string) => {
    dispatch({ type: 'DELETE_TASK', payload: taskId });
  };

  const duplicateTask = (taskId: string, targetPageId?: string) => {
    dispatch({ type: 'DUPLICATE_TASK', payload: { originalTaskId: taskId, targetPageId } });
  };

  const addPage = (pageData: Omit<Page, 'id' | 'createdAt' | 'tasks'>) => {
    const newPage: Page = {
      ...pageData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      tasks: []
    };
    dispatch({ type: 'ADD_PAGE', payload: newPage });
  };

  const updatePage = (pageId: string, updates: Partial<Page>) => {
    dispatch({ type: 'UPDATE_PAGE', payload: { pageId, updates } });
  };

  const deletePage = (pageId: string) => {
    dispatch({ type: 'DELETE_PAGE', payload: pageId });
  };

  const moveTask = (taskId: string, targetPageId?: string, targetIndex?: number) => {
    dispatch({ type: 'MOVE_TASK', payload: { taskId, targetPageId, targetIndex } });
  };

  const searchTasks = (query: string): Task[] => {
    if (!query.trim()) return state.unassignedTasks;
    
    const lowerQuery = query.toLowerCase();
    return state.unassignedTasks.filter(task =>
      task.title.toLowerCase().includes(lowerQuery) ||
      task.description.toLowerCase().includes(lowerQuery)
    );
  };

  return (
    <TaskContext.Provider value={{
      state,
      addTask,
      updateTask,
      deleteTask,
      duplicateTask,
      addPage,
      updatePage,
      deletePage,
      moveTask,
      searchTasks
    }}>
      {children}
    </TaskContext.Provider>
  );
};

export const useTask = () => {
  const context = useContext(TaskContext);
  if (context === undefined) {
    throw new Error('useTask must be used within a TaskProvider');
  }
  return context;
};
