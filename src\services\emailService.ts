// Email Service for sending notifications
// This uses Supabase Edge Functions for email sending

import { supabase } from '../lib/supabase';

export interface EmailNotification {
  to: string;
  subject: string;
  html: string;
  type: 'invitation' | 'task_assignment' | 'task_update' | 'workspace_update';
}

export interface WorkspaceInvitationData {
  workspaceName: string;
  inviterName: string;
  inviteCode: string;
  role: string;
  workspaceDescription?: string;
}

export interface TaskAssignmentData {
  taskTitle: string;
  taskDescription: string;
  workspaceName: string;
  assignerName: string;
  dueDate?: string;
  pageTitle: string;
}

export interface TaskUpdateData {
  taskTitle: string;
  updateType: 'status_change' | 'comment' | 'assignment' | 'due_date';
  updaterName: string;
  workspaceName: string;
  pageTitle: string;
  details: string;
}

class EmailService {
  private baseUrl = window.location.origin;

  // Generate workspace invitation email
  generateInvitationEmail(data: WorkspaceInvitationData): string {
    const joinUrl = `${this.baseUrl}/join/${data.inviteCode}`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Workspace Invitation</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; border-radius: 0 0 8px 8px; }
          .workspace-info { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 You're Invited!</h1>
            <p>Join ${data.workspaceName} on Page Task Hub</p>
          </div>
          
          <div class="content">
            <h2>Hi there!</h2>
            <p><strong>${data.inviterName}</strong> has invited you to join the <strong>${data.workspaceName}</strong> workspace as a <strong>${data.role}</strong>.</p>
            
            ${data.workspaceDescription ? `
              <div class="workspace-info">
                <h3>About this workspace:</h3>
                <p>${data.workspaceDescription}</p>
              </div>
            ` : ''}
            
            <p>Page Task Hub is a collaborative task management platform where teams can organize projects, track progress, and work together in real-time.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${joinUrl}" class="button">Join Workspace</a>
            </div>
            
            <p><small>Or copy and paste this link in your browser: <br><code>${joinUrl}</code></small></p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e1e5e9;">
            
            <h3>What you can do as a ${data.role}:</h3>
            <ul>
              ${data.role === 'owner' ? `
                <li>Full workspace management</li>
                <li>Invite and manage members</li>
                <li>Create and organize pages</li>
                <li>Manage all tasks and projects</li>
              ` : data.role === 'admin' ? `
                <li>Invite and manage members</li>
                <li>Create and organize pages</li>
                <li>Manage tasks and projects</li>
                <li>Configure workspace settings</li>
              ` : `
                <li>Create and manage tasks</li>
                <li>Collaborate on projects</li>
                <li>Comment and participate in discussions</li>
                <li>View workspace activity</li>
              `}
            </ul>
          </div>
          
          <div class="footer">
            <p>This invitation was sent by ${data.inviterName} from Page Task Hub.</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate task assignment email
  generateTaskAssignmentEmail(data: TaskAssignmentData): string {
    const taskUrl = `${this.baseUrl}/workspace`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Task Assignment</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
          .task-info { background: #f0fdf4; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #10b981; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; border-radius: 0 0 8px 8px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📋 New Task Assignment</h1>
            <p>You have a new task in ${data.workspaceName}</p>
          </div>
          
          <div class="content">
            <h2>Hi there!</h2>
            <p><strong>${data.assignerName}</strong> has assigned you a new task in the <strong>${data.workspaceName}</strong> workspace.</p>
            
            <div class="task-info">
              <h3>${data.taskTitle}</h3>
              <p><strong>Page:</strong> ${data.pageTitle}</p>
              ${data.taskDescription ? `<p><strong>Description:</strong> ${data.taskDescription}</p>` : ''}
              ${data.dueDate ? `<p><strong>Due Date:</strong> ${data.dueDate}</p>` : ''}
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${taskUrl}" class="button">View Task</a>
            </div>
          </div>
          
          <div class="footer">
            <p>This notification was sent from Page Task Hub.</p>
            <p>You can manage your notification preferences in your workspace settings.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate task update email
  generateTaskUpdateEmail(data: TaskUpdateData): string {
    const taskUrl = `${this.baseUrl}/workspace`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Task Update</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
          .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
          .update-info { background: #eff6ff; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #3b82f6; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; border-radius: 0 0 8px 8px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔄 Task Update</h1>
            <p>Updates in ${data.workspaceName}</p>
          </div>
          
          <div class="content">
            <h2>Task Updated</h2>
            <p><strong>${data.updaterName}</strong> made changes to a task you're involved with.</p>
            
            <div class="update-info">
              <h3>${data.taskTitle}</h3>
              <p><strong>Page:</strong> ${data.pageTitle}</p>
              <p><strong>Update:</strong> ${data.details}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${taskUrl}" class="button">View Task</a>
            </div>
          </div>
          
          <div class="footer">
            <p>This notification was sent from Page Task Hub.</p>
            <p>You can manage your notification preferences in your workspace settings.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Send email via Supabase Edge Function
  async sendEmail(notification: EmailNotification): Promise<boolean> {
    try {
      // In a real implementation, you would call a Supabase Edge Function
      // For now, we'll simulate the email sending and log it
      console.log('📧 Email would be sent:', {
        to: notification.to,
        subject: notification.subject,
        type: notification.type
      });

      // Simulate API call to email service
      // const { data, error } = await supabase.functions.invoke('send-email', {
      //   body: notification
      // });

      // For demo purposes, we'll just return true
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  // Send workspace invitation email
  async sendWorkspaceInvitation(email: string, data: WorkspaceInvitationData): Promise<boolean> {
    const html = this.generateInvitationEmail(data);
    
    return this.sendEmail({
      to: email,
      subject: `You're invited to join ${data.workspaceName} on Page Task Hub`,
      html,
      type: 'invitation'
    });
  }

  // Send task assignment email
  async sendTaskAssignment(email: string, data: TaskAssignmentData): Promise<boolean> {
    const html = this.generateTaskAssignmentEmail(data);
    
    return this.sendEmail({
      to: email,
      subject: `New task assigned: ${data.taskTitle}`,
      html,
      type: 'task_assignment'
    });
  }

  // Send task update email
  async sendTaskUpdate(email: string, data: TaskUpdateData): Promise<boolean> {
    const html = this.generateTaskUpdateEmail(data);
    
    return this.sendEmail({
      to: email,
      subject: `Task updated: ${data.taskTitle}`,
      html,
      type: 'task_update'
    });
  }
}

export const emailService = new EmailService();
