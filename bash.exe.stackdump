Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE4E720000 ntdll.dll
7FFE4C4C0000 KERNEL32.DLL
7FFE4BAC0000 KERNELBASE.dll
7FFE4D410000 USER32.dll
7FFE4BA90000 win32u.dll
7FFE4E6A0000 GDI32.dll
000210040000 msys-2.0.dll
7FFE4C0C0000 gdi32full.dll
7FFE4C200000 msvcp_win.dll
7FFE4C2B0000 ucrtbase.dll
7FFE4E490000 advapi32.dll
7FFE4D630000 msvcrt.dll
7FFE4D6E0000 sechost.dll
7FFE4DE70000 RPCRT4.dll
7FFE4AF70000 CRYPTBASE.DLL
7FFE4B9F0000 bcryptPrimitives.dll
7FFE4D5F0000 IMM32.DLL
