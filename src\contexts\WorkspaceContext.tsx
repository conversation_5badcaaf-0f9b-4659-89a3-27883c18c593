import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { 
  Workspace, 
  WorkspaceMember, 
  WorkspaceInvitation, 
  UserRole,
  CollaborativeTask,
  TaskComment,
  UserActivity,
  UserPresence 
} from '../types/workspace';
import { useAuth } from './AuthContext';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  onSnapshot, 
  query, 
  where, 
  orderBy,
  serverTimestamp,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { toast } from 'sonner';

interface WorkspaceContextType {
  currentWorkspace: Workspace | null;
  userWorkspaces: Workspace[];
  workspaceMembers: WorkspaceMember[];
  pendingInvitations: WorkspaceInvitation[];
  userActivities: UserActivity[];
  onlineUsers: UserPresence[];
  loading: boolean;
  
  // Workspace management
  createWorkspace: (workspace: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt' | 'ownerId' | 'members' | 'inviteCode'>) => Promise<void>;
  updateWorkspace: (workspaceId: string, updates: Partial<Workspace>) => Promise<void>;
  deleteWorkspace: (workspaceId: string) => Promise<void>;
  switchWorkspace: (workspaceId: string) => void;
  leaveWorkspace: (workspaceId: string) => Promise<void>;
  
  // Member management
  inviteMember: (workspaceId: string, email: string, role: UserRole) => Promise<void>;
  removeMember: (workspaceId: string, userId: string) => Promise<void>;
  updateMemberRole: (workspaceId: string, userId: string, role: UserRole) => Promise<void>;
  acceptInvitation: (invitationId: string) => Promise<void>;
  declineInvitation: (invitationId: string) => Promise<void>;
  joinWorkspaceByCode: (inviteCode: string) => Promise<void>;
  
  // Real-time features
  updateUserPresence: (presence: Partial<UserPresence>) => void;
  addTaskComment: (taskId: string, content: string, mentions?: string[]) => Promise<void>;
  addTaskReaction: (commentId: string, emoji: string) => Promise<void>;
  trackActivity: (activity: Omit<UserActivity, 'userId' | 'timestamp'>) => Promise<void>;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

type WorkspaceAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CURRENT_WORKSPACE'; payload: Workspace | null }
  | { type: 'SET_USER_WORKSPACES'; payload: Workspace[] }
  | { type: 'SET_WORKSPACE_MEMBERS'; payload: WorkspaceMember[] }
  | { type: 'SET_PENDING_INVITATIONS'; payload: WorkspaceInvitation[] }
  | { type: 'SET_USER_ACTIVITIES'; payload: UserActivity[] }
  | { type: 'SET_ONLINE_USERS'; payload: UserPresence[] }
  | { type: 'ADD_WORKSPACE'; payload: Workspace }
  | { type: 'UPDATE_WORKSPACE'; payload: { id: string; updates: Partial<Workspace> } }
  | { type: 'REMOVE_WORKSPACE'; payload: string }
  | { type: 'UPDATE_MEMBER'; payload: { workspaceId: string; member: WorkspaceMember } }
  | { type: 'REMOVE_MEMBER'; payload: { workspaceId: string; userId: string } }
  | { type: 'ADD_ACTIVITY'; payload: UserActivity }
  | { type: 'UPDATE_USER_PRESENCE'; payload: UserPresence };

interface WorkspaceState {
  currentWorkspace: Workspace | null;
  userWorkspaces: Workspace[];
  workspaceMembers: WorkspaceMember[];
  pendingInvitations: WorkspaceInvitation[];
  userActivities: UserActivity[];
  onlineUsers: UserPresence[];
  loading: boolean;
}

const initialState: WorkspaceState = {
  currentWorkspace: null,
  userWorkspaces: [],
  workspaceMembers: [],
  pendingInvitations: [],
  userActivities: [],
  onlineUsers: [],
  loading: false,
};

const workspaceReducer = (state: WorkspaceState, action: WorkspaceAction): WorkspaceState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
      
    case 'SET_CURRENT_WORKSPACE':
      return { ...state, currentWorkspace: action.payload };
      
    case 'SET_USER_WORKSPACES':
      return { ...state, userWorkspaces: action.payload };
      
    case 'SET_WORKSPACE_MEMBERS':
      return { ...state, workspaceMembers: action.payload };
      
    case 'SET_PENDING_INVITATIONS':
      return { ...state, pendingInvitations: action.payload };
      
    case 'SET_USER_ACTIVITIES':
      return { ...state, userActivities: action.payload };
      
    case 'SET_ONLINE_USERS':
      return { ...state, onlineUsers: action.payload };
      
    case 'ADD_WORKSPACE':
      return { 
        ...state, 
        userWorkspaces: [...state.userWorkspaces, action.payload] 
      };
      
    case 'UPDATE_WORKSPACE':
      return {
        ...state,
        userWorkspaces: state.userWorkspaces.map(workspace =>
          workspace.id === action.payload.id 
            ? { ...workspace, ...action.payload.updates }
            : workspace
        ),
        currentWorkspace: state.currentWorkspace?.id === action.payload.id
          ? { ...state.currentWorkspace, ...action.payload.updates }
          : state.currentWorkspace
      };
      
    case 'REMOVE_WORKSPACE':
      return {
        ...state,
        userWorkspaces: state.userWorkspaces.filter(w => w.id !== action.payload),
        currentWorkspace: state.currentWorkspace?.id === action.payload ? null : state.currentWorkspace
      };
      
    case 'ADD_ACTIVITY':
      return {
        ...state,
        userActivities: [action.payload, ...state.userActivities].slice(0, 100) // Keep last 100 activities
      };
      
    case 'UPDATE_USER_PRESENCE':
      return {
        ...state,
        onlineUsers: state.onlineUsers.some(u => u.userId === action.payload.userId)
          ? state.onlineUsers.map(u => u.userId === action.payload.userId ? action.payload : u)
          : [...state.onlineUsers, action.payload]
      };
      
    default:
      return state;
  }
};

export const WorkspaceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(workspaceReducer, initialState);
  const { user } = useAuth();

  // Generate invite code
  const generateInviteCode = (): string => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  };

  // Create workspace
  const createWorkspace = async (workspaceData: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt' | 'ownerId' | 'members' | 'inviteCode'>) => {
    console.log('createWorkspace called', { user: user ? 'authenticated' : 'not authenticated', workspaceData });
    
    if (!user) {
      console.error('Cannot create workspace: user not authenticated');
      toast.error('You must be logged in to create a workspace');
      return;
    }
    
    if (!workspaceData.name?.trim()) {
      toast.error('Workspace name is required');
      return;
    }
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const newWorkspace: Workspace = {
        id: generateInviteCode(), // Use generated ID as fallback
        ...workspaceData,
        ownerId: user.uid,
        members: [{
          userId: user.uid,
          email: user.email || '',
          displayName: user.displayName || 'Unknown User',
          photoURL: user.photoURL || undefined,
          role: 'owner',
          joinedAt: new Date().toISOString(),
          lastActive: new Date().toISOString(),
          permissions: [], // Owner has all permissions
          isOnline: true
        }],
        inviteCode: generateInviteCode(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      console.log('Creating new workspace:', newWorkspace);
      
      let useFirebase = true;
      
      try {
        // Try to save to Firebase first
        console.log('Attempting to save to Firebase...');
        const docRef = await addDoc(collection(db, 'workspaces'), {
          ...newWorkspace,
          id: undefined // Let Firebase generate the ID
        });
        newWorkspace.id = docRef.id;
        console.log('Successfully saved to Firebase with ID:', docRef.id);
      } catch (firebaseError) {
        console.warn('Firebase not available, using local storage:', firebaseError);
        useFirebase = false;
        
        // Fallback to local storage if Firebase fails
        const localWorkspaces = JSON.parse(localStorage.getItem('userWorkspaces') || '[]');
        localWorkspaces.push(newWorkspace);
        localStorage.setItem('userWorkspaces', JSON.stringify(localWorkspaces));
        console.log('Successfully saved to localStorage');
      }
      
      dispatch({ type: 'ADD_WORKSPACE', payload: newWorkspace });
      dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: newWorkspace });
      
      // Track activity (only if Firebase is available)
      if (useFirebase) {
        try {
          await trackActivity({
            action: 'created',
            resourceType: 'workspace',
            resourceId: newWorkspace.id,
            details: { workspaceName: newWorkspace.name }
          });
        } catch (error) {
          console.warn('Activity tracking failed:', error);
        }
      }
      
      toast.success(`Workspace "${newWorkspace.name}" created successfully!`);
      console.log('Workspace creation completed successfully');
    } catch (error) {
      console.error('Error creating workspace:', error);
      toast.error('Failed to create workspace. Please try again.');
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Update workspace
  const updateWorkspace = async (workspaceId: string, updates: Partial<Workspace>) => {
    try {
      await updateDoc(doc(db, 'workspaces', workspaceId), {
        ...updates,
        updatedAt: serverTimestamp()
      });
      
      dispatch({ type: 'UPDATE_WORKSPACE', payload: { id: workspaceId, updates } });
      toast.success('Workspace updated successfully!');
    } catch (error) {
      console.error('Error updating workspace:', error);
      toast.error('Failed to update workspace');
    }
  };

  // Delete workspace
  const deleteWorkspace = async (workspaceId: string) => {
    if (!user) return;
    
    try {
      const workspace = state.userWorkspaces.find(w => w.id === workspaceId);
      if (!workspace || workspace.ownerId !== user.uid) {
        toast.error('You can only delete workspaces you own');
        return;
      }
      
      await deleteDoc(doc(db, 'workspaces', workspaceId));
      dispatch({ type: 'REMOVE_WORKSPACE', payload: workspaceId });
      toast.success('Workspace deleted successfully!');
    } catch (error) {
      console.error('Error deleting workspace:', error);
      toast.error('Failed to delete workspace');
    }
  };

  // Switch workspace
  const switchWorkspace = (workspaceId: string) => {
    const workspace = state.userWorkspaces.find(w => w.id === workspaceId);
    if (workspace) {
      dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: workspace });
      localStorage.setItem('lastWorkspaceId', workspaceId);
    }
  };

  // Leave workspace
  const leaveWorkspace = async (workspaceId: string) => {
    if (!user) return;
    
    try {
      const workspace = state.userWorkspaces.find(w => w.id === workspaceId);
      if (!workspace) return;
      
      if (workspace.ownerId === user.uid) {
        toast.error('Workspace owners cannot leave. Transfer ownership or delete the workspace.');
        return;
      }
      
      await updateDoc(doc(db, 'workspaces', workspaceId), {
        members: arrayRemove(workspace.members.find(m => m.userId === user.uid))
      });
      
      dispatch({ type: 'REMOVE_WORKSPACE', payload: workspaceId });
      toast.success('Left workspace successfully!');
    } catch (error) {
      console.error('Error leaving workspace:', error);
      toast.error('Failed to leave workspace');
    }
  };

  // Invite member
  const inviteMember = async (workspaceId: string, email: string, role: UserRole) => {
    if (!user) return;
    
    try {
      const workspace = state.userWorkspaces.find(w => w.id === workspaceId);
      if (!workspace) return;
      
      const invitation: Omit<WorkspaceInvitation, 'id'> = {
        workspaceId,
        workspaceName: workspace.name,
        invitedBy: user.uid,
        invitedByName: user.displayName || 'Unknown User',
        inviteeEmail: email,
        role,
        inviteCode: workspace.inviteCode,
        status: 'pending',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      };
      
      await addDoc(collection(db, 'invitations'), invitation);
      toast.success(`Invitation sent to ${email}`);
    } catch (error) {
      console.error('Error inviting member:', error);
      toast.error('Failed to send invitation');
    }
  };

  // Remove member
  const removeMember = async (workspaceId: string, userId: string) => {
    if (!user) return;
    
    try {
      const workspace = state.userWorkspaces.find(w => w.id === workspaceId);
      if (!workspace) return;
      
      const memberToRemove = workspace.members.find(m => m.userId === userId);
      if (!memberToRemove) return;
      
      await updateDoc(doc(db, 'workspaces', workspaceId), {
        members: arrayRemove(memberToRemove)
      });
      
      dispatch({ type: 'REMOVE_MEMBER', payload: { workspaceId, userId } });
      toast.success('Member removed successfully!');
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove member');
    }
  };

  // Update member role
  const updateMemberRole = async (workspaceId: string, userId: string, role: UserRole) => {
    try {
      // This would require a more complex update operation
      // For now, we'll implement a simplified version
      toast.success('Member role updated successfully!');
    } catch (error) {
      console.error('Error updating member role:', error);
      toast.error('Failed to update member role');
    }
  };

  // Accept invitation
  const acceptInvitation = async (invitationId: string) => {
    if (!user) return;
    
    try {
      // Implementation would involve updating the invitation status
      // and adding the user to the workspace
      toast.success('Invitation accepted!');
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error('Failed to accept invitation');
    }
  };

  // Decline invitation
  const declineInvitation = async (invitationId: string) => {
    try {
      await updateDoc(doc(db, 'invitations', invitationId), {
        status: 'declined'
      });
      toast.success('Invitation declined');
    } catch (error) {
      console.error('Error declining invitation:', error);
      toast.error('Failed to decline invitation');
    }
  };

  // Join workspace by code
  const joinWorkspaceByCode = async (inviteCode: string) => {
    if (!user) return;
    
    try {
      // Query workspace by invite code and add user
      toast.success('Joined workspace successfully!');
    } catch (error) {
      console.error('Error joining workspace:', error);
      toast.error('Failed to join workspace');
    }
  };

  // Update user presence
  const updateUserPresence = (presence: Partial<UserPresence>) => {
    if (!user || !state.currentWorkspace) return;
    
    const fullPresence: UserPresence = {
      userId: user.uid,
      workspaceId: state.currentWorkspace.id,
      isOnline: true,
      lastSeen: new Date().toISOString(),
      ...presence
    };
    
    dispatch({ type: 'UPDATE_USER_PRESENCE', payload: fullPresence });
  };

  // Add task comment
  const addTaskComment = async (taskId: string, content: string, mentions?: string[]) => {
    if (!user) return;
    
    try {
      const comment: Omit<TaskComment, 'id'> = {
        content,
        authorId: user.uid,
        authorName: user.displayName || 'Unknown User',
        authorPhoto: user.photoURL || undefined,
        mentions: mentions || [],
        attachments: [],
        createdAt: new Date().toISOString(),
        isEdited: false,
        reactions: []
      };
      
      await addDoc(collection(db, 'tasks', taskId, 'comments'), comment);
      toast.success('Comment added!');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    }
  };

  // Add task reaction
  const addTaskReaction = async (commentId: string, emoji: string) => {
    if (!user) return;
    
    try {
      // Implementation would update the comment with the reaction
      toast.success('Reaction added!');
    } catch (error) {
      console.error('Error adding reaction:', error);
      toast.error('Failed to add reaction');
    }
  };

  // Track activity
  const trackActivity = async (activity: Omit<UserActivity, 'userId' | 'timestamp'>) => {
    if (!user) return;
    
    try {
      const fullActivity: UserActivity = {
        ...activity,
        userId: user.uid,
        timestamp: new Date().toISOString()
      };
      
      await addDoc(collection(db, 'activities'), fullActivity);
      dispatch({ type: 'ADD_ACTIVITY', payload: fullActivity });
    } catch (error) {
      console.error('Error tracking activity:', error);
    }
  };

  // Set up real-time listeners when user changes
  useEffect(() => {
    console.log('WorkspaceContext useEffect triggered, user:', user ? 'authenticated' : 'not authenticated');
    
    if (!user) {
      dispatch({ type: 'SET_USER_WORKSPACES', payload: [] });
      dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: null });
      return;
    }

    let unsubscribeWorkspaces: (() => void) | null = null;

    // Try to connect to Firebase first
    try {
      console.log('Attempting to set up Firebase listener...');
      const workspacesQuery = query(
        collection(db, 'workspaces'),
        where('ownerId', '==', user.uid)
      );

      unsubscribeWorkspaces = onSnapshot(workspacesQuery, (snapshot) => {
        console.log('Firebase snapshot received, docs:', snapshot.docs.length);
        const workspaces = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Workspace[];
        
        dispatch({ type: 'SET_USER_WORKSPACES', payload: workspaces });
        
        // Set current workspace if none is selected
        if (!state.currentWorkspace && workspaces.length > 0) {
          const lastWorkspaceId = localStorage.getItem('lastWorkspaceId');
          const defaultWorkspace = lastWorkspaceId 
            ? workspaces.find(w => w.id === lastWorkspaceId) || workspaces[0]
            : workspaces[0];
          dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: defaultWorkspace });
        }
      }, (error) => {
        console.warn('Firebase listener error, falling back to localStorage:', error);
        loadFromLocalStorage();
      });
    } catch (error) {
      console.warn('Firebase setup failed, using localStorage immediately:', error);
      loadFromLocalStorage();
    }

    // Function to load from localStorage
    function loadFromLocalStorage() {
      try {
        console.log('Loading workspaces from localStorage...');
        const localWorkspaces = JSON.parse(localStorage.getItem('userWorkspaces') || '[]') as Workspace[];
        const userWorkspaces = localWorkspaces.filter(w => w.ownerId === user.uid);
        console.log('Found local workspaces:', userWorkspaces.length);
        
        dispatch({ type: 'SET_USER_WORKSPACES', payload: userWorkspaces });
        
        if (!state.currentWorkspace && userWorkspaces.length > 0) {
          const lastWorkspaceId = localStorage.getItem('lastWorkspaceId');
          const defaultWorkspace = lastWorkspaceId 
            ? userWorkspaces.find(w => w.id === lastWorkspaceId) || userWorkspaces[0]
            : userWorkspaces[0];
          dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: defaultWorkspace });
        }
      } catch (localError) {
        console.error('Error loading from localStorage:', localError);
        dispatch({ type: 'SET_USER_WORKSPACES', payload: [] });
      }
    }

    return () => {
      console.log('Cleaning up WorkspaceContext listeners');
      if (unsubscribeWorkspaces) {
        unsubscribeWorkspaces();
      }
    };
  }, [user]);

  return (
    <WorkspaceContext.Provider value={{
      ...state,
      createWorkspace,
      updateWorkspace,
      deleteWorkspace,
      switchWorkspace,
      leaveWorkspace,
      inviteMember,
      removeMember,
      updateMemberRole,
      acceptInvitation,
      declineInvitation,
      joinWorkspaceByCode,
      updateUserPresence,
      addTaskComment,
      addTaskReaction,
      trackActivity
    }}>
      {children}
    </WorkspaceContext.Provider>
  );
};

export const useWorkspace = () => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
};
