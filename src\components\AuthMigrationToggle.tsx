import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { AlertTriangle, Database, Users, CheckCircle2 } from 'lucide-react';

interface AuthMigrationToggleProps {
  useSupabase: boolean;
  onToggle: (useSupabase: boolean) => void;
  firebaseUser?: any;
  supabaseUser?: any;
}

const AuthMigrationToggle: React.FC<AuthMigrationToggleProps> = ({
  useSupabase,
  onToggle,
  firebaseUser,
  supabaseUser
}) => {
  const [isToggling, setIsToggling] = useState(false);

  const handleToggle = async (checked: boolean) => {
    setIsToggling(true);
    
    // Add a small delay to show the loading state
    await new Promise(resolve => setTimeout(resolve, 500));
    
    onToggle(checked);
    setIsToggling(false);
  };

  return (
    <Card className="border-2 border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Authentication Migration Control
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Toggle Switch */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div className="flex items-center gap-3">
            <Users className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-medium">Authentication Provider</p>
              <p className="text-sm text-gray-600">
                Switch between Firebase and Supabase auth
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <span className={`text-sm ${!useSupabase ? 'font-medium' : 'text-gray-500'}`}>
              Firebase
            </span>
            <Switch
              checked={useSupabase}
              onCheckedChange={handleToggle}
              disabled={isToggling}
            />
            <span className={`text-sm ${useSupabase ? 'font-medium' : 'text-gray-500'}`}>
              Supabase
            </span>
          </div>
        </div>

        {/* Current Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Firebase Status */}
          <div className="p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm">Firebase Auth</h4>
              <Badge variant={!useSupabase ? "default" : "secondary"}>
                {!useSupabase ? "Active" : "Inactive"}
              </Badge>
            </div>
            <div className="text-xs text-gray-600">
              {firebaseUser ? (
                <div className="flex items-center gap-1">
                  <CheckCircle2 className="h-3 w-3 text-green-500" />
                  <span>Signed in: {firebaseUser.email}</span>
                </div>
              ) : (
                <span>Not signed in</span>
              )}
            </div>
          </div>

          {/* Supabase Status */}
          <div className="p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm">Supabase Auth</h4>
              <Badge variant={useSupabase ? "default" : "secondary"}>
                {useSupabase ? "Active" : "Inactive"}
              </Badge>
            </div>
            <div className="text-xs text-gray-600">
              {supabaseUser ? (
                <div className="flex items-center gap-1">
                  <CheckCircle2 className="h-3 w-3 text-green-500" />
                  <span>Signed in: {supabaseUser.email}</span>
                </div>
              ) : (
                <span>Not signed in</span>
              )}
            </div>
          </div>
        </div>

        {/* Warning */}
        <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-yellow-800">Migration Mode Active</p>
            <p className="text-yellow-700">
              This toggle allows testing both authentication systems. 
              Once migration is complete, this will be removed.
            </p>
          </div>
        </div>

        {/* Instructions */}
        <div className="text-xs text-gray-600 space-y-1">
          <p><strong>Testing Instructions:</strong></p>
          <p>1. Test Firebase auth (current system)</p>
          <p>2. Switch to Supabase and test new auth</p>
          <p>3. Verify both systems work correctly</p>
          <p>4. Once confirmed, we'll remove Firebase completely</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthMigrationToggle;
