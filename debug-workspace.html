<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workspace Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Workspace Creation Debug</h1>
    
    <div id="debug-info" class="debug">
        <h3>Debug Information:</h3>
        <div id="console-logs"></div>
    </div>
    
    <div>
        <h3>Test Workspace Creation:</h3>
        <button onclick="testLocalStorage()">Test LocalStorage</button>
        <button onclick="testFirebase()">Test Firebase</button>
        <button onclick="clearStorage()">Clear Storage</button>
        <button onclick="viewStorage()">View Storage</button>
    </div>
    
    <script>
        const debugDiv = document.getElementById('console-logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${new Date().toISOString()}] ${message}`;
            debugDiv.appendChild(div);
            console.log(message);
        }
        
        function testLocalStorage() {
            log('Testing localStorage...', 'info');
            
            try {
                const testWorkspace = {
                    id: 'test_' + Date.now(),
                    name: 'Test Workspace',
                    description: 'A test workspace',
                    ownerId: 'test_user_123',
                    members: [{
                        userId: 'test_user_123',
                        email: '<EMAIL>',
                        displayName: 'Test User',
                        role: 'owner',
                        joinedAt: new Date().toISOString(),
                        lastActive: new Date().toISOString(),
                        permissions: [],
                        isOnline: true
                    }],
                    inviteCode: 'test123',
                    settings: {
                        isPublic: false,
                        allowGuestAccess: false,
                        requireApprovalForJoining: false,
                        defaultMemberRole: 'member',
                        notificationSettings: {
                            emailNotifications: true,
                            taskAssignments: true,
                            taskComments: true,
                            taskStatusChanges: true,
                            workspaceUpdates: true
                        }
                    },
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                const existingWorkspaces = JSON.parse(localStorage.getItem('userWorkspaces') || '[]');
                existingWorkspaces.push(testWorkspace);
                localStorage.setItem('userWorkspaces', JSON.stringify(existingWorkspaces));
                
                log('LocalStorage test successful!', 'success');
                log(`Workspace "${testWorkspace.name}" saved with ID: ${testWorkspace.id}`, 'success');
                
            } catch (error) {
                log(`LocalStorage test failed: ${error.message}`, 'error');
            }
        }
        
        function testFirebase() {
            log('Testing Firebase connectivity...', 'info');
            
            // This is a simplified test to see if Firebase is accessible
            try {
                if (typeof window !== 'undefined') {
                    log('Window object available', 'success');
                    
                    // Try to access the main app
                    fetch('/').then(response => {
                        log(`Main app response: ${response.status}`, response.ok ? 'success' : 'error');
                    }).catch(error => {
                        log(`Fetch error: ${error.message}`, 'error');
                    });
                }
            } catch (error) {
                log(`Firebase test failed: ${error.message}`, 'error');
            }
        }
        
        function clearStorage() {
            localStorage.removeItem('userWorkspaces');
            localStorage.removeItem('lastWorkspaceId');
            log('LocalStorage cleared', 'success');
        }
        
        function viewStorage() {
            const workspaces = localStorage.getItem('userWorkspaces');
            const lastWorkspaceId = localStorage.getItem('lastWorkspaceId');
            
            log(`Stored workspaces: ${workspaces || 'none'}`, 'info');
            log(`Last workspace ID: ${lastWorkspaceId || 'none'}`, 'info');
            
            if (workspaces) {
                try {
                    const parsed = JSON.parse(workspaces);
                    log(`Found ${parsed.length} workspaces in storage`, 'success');
                    parsed.forEach((ws, index) => {
                        log(`  ${index + 1}. ${ws.name} (ID: ${ws.id})`, 'info');
                    });
                } catch (error) {
                    log(`Error parsing workspaces: ${error.message}`, 'error');
                }
            }
        }
        
        // Initialize
        log('Debug page loaded', 'success');
        viewStorage();
    </script>
</body>
</html>
