

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { SupabaseAuthProvider } from "./contexts/SupabaseAuthContext";
import { WorkspaceProvider } from "./contexts/WorkspaceContext";
import { TaskProvider } from "./contexts/TaskContext";
import ErrorBoundary from "./components/ErrorBoundary";
import ProtectedRoute from "./components/ProtectedRoute";
import CollaborationOverlay from "./components/CollaborationOverlay";
import Navbar from "./components/Navbar";
import Home from "./pages/Home";
import Tasker from "./pages/Tasker";
import AddPage from "./pages/AddPage";
import JoinWorkspace from "./pages/JoinWorkspace";
import WebsiteManager from "./components/WebsiteManager";
import SupabaseTest from "./pages/SupabaseTest";
import AuthMigration from "./pages/AuthMigration";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          {/* Use both auth providers during migration for testing */}
          <AuthProvider>
            <SupabaseAuthProvider>
              <WorkspaceProvider>
                <TaskProvider>
                  <Toaster />
                  <Sonner />
                  <BrowserRouter>
                    <div className="min-h-screen">
                      <Routes>
                        {/* Public route for joining workspaces */}
                        <Route path="/join/:inviteCode" element={<JoinWorkspace />} />

                        {/* Protected routes */}
                        <Route path="/*" element={
                          <ProtectedRoute>
                            <CollaborationOverlay>
                              <Navbar />
                              <Routes>
                                <Route path="/" element={<Home />} />
                                <Route path="/tasker" element={<Tasker />} />
                                <Route path="/websites" element={<WebsiteManager />} />
                                <Route path="/add-page" element={<AddPage />} />
                                <Route path="/supabase-test" element={<SupabaseTest />} />
                                <Route path="/auth-migration" element={<AuthMigration />} />
                                <Route path="*" element={<NotFound />} />
                              </Routes>
                            </CollaborationOverlay>
                          </ProtectedRoute>
                        } />
                      </Routes>
                    </div>
                  </BrowserRouter>
                </TaskProvider>
              </WorkspaceProvider>
            </SupabaseAuthProvider>
          </AuthProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
