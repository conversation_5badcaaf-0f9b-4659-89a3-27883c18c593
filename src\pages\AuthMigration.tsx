import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { AlertTriangle, Database, Users, CheckCircle2, ArrowRight, TestTube } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';

const AuthMigration: React.FC = () => {
  const [useSupabase, setUseSupabase] = useState(false);
  
  // Get auth states from both providers
  const firebaseAuth = useAuth();
  const supabaseAuth = useSupabaseAuth();

  const handleToggle = (checked: boolean) => {
    setUseSupabase(checked);
    // In a real implementation, this would update the global state
    // For now, this is just for demonstration
  };

  const testFirebaseAuth = async () => {
    try {
      await firebaseAuth.signInWithGoogle();
    } catch (error) {
      console.error('Firebase auth test failed:', error);
    }
  };

  const testSupabaseAuth = async () => {
    try {
      await supabaseAuth.signInWithGoogle();
    } catch (error) {
      console.error('Supabase auth test failed:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">🔄 Authentication Migration</h1>
        <p className="text-gray-600">
          Test and migrate from Firebase Auth to Supabase Auth step by step.
        </p>
      </div>

      {/* Migration Progress */}
      <Card className="mb-6 border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Migration Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <span className="text-sm">Supabase Setup Complete</span>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-400" />
            <div className="flex items-center gap-2">
              <TestTube className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">Testing Auth Migration</span>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-400" />
            <div className="flex items-center gap-2">
              <div className="h-5 w-5 rounded-full border-2 border-gray-300"></div>
              <span className="text-sm text-gray-500">Complete Migration</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Auth Provider Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Firebase Auth Card */}
        <Card className="border-orange-200">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Firebase Auth
              </span>
              <Badge variant="secondary">Current</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Status:</p>
              {firebaseAuth.user ? (
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Signed in as {firebaseAuth.user.email}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full border-2 border-gray-300"></div>
                  <span className="text-sm text-gray-500">Not signed in</span>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Loading:</p>
              <Badge variant={firebaseAuth.loading ? "destructive" : "default"}>
                {firebaseAuth.loading ? "Loading..." : "Ready"}
              </Badge>
            </div>

            <Button 
              onClick={testFirebaseAuth}
              variant="outline"
              size="sm"
              className="w-full"
              disabled={firebaseAuth.loading}
            >
              Test Firebase Sign In
            </Button>

            {firebaseAuth.user && (
              <Button 
                onClick={firebaseAuth.signOut}
                variant="outline"
                size="sm"
                className="w-full"
              >
                Sign Out Firebase
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Supabase Auth Card */}
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Supabase Auth
              </span>
              <Badge variant="default">New</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Status:</p>
              {supabaseAuth.user ? (
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Signed in as {supabaseAuth.user.email}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full border-2 border-gray-300"></div>
                  <span className="text-sm text-gray-500">Not signed in</span>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Loading:</p>
              <Badge variant={supabaseAuth.loading ? "destructive" : "default"}>
                {supabaseAuth.loading ? "Loading..." : "Ready"}
              </Badge>
            </div>

            <Button 
              onClick={testSupabaseAuth}
              variant="outline"
              size="sm"
              className="w-full"
              disabled={supabaseAuth.loading}
            >
              Test Supabase Sign In
            </Button>

            {supabaseAuth.user && (
              <Button 
                onClick={supabaseAuth.signOut}
                variant="outline"
                size="sm"
                className="w-full"
              >
                Sign Out Supabase
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Migration Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Migration Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium">1</div>
              <div>
                <p className="font-medium">Test Firebase Authentication</p>
                <p className="text-sm text-gray-600">Click "Test Firebase Sign In" to verify current auth is working</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium">2</div>
              <div>
                <p className="font-medium">Test Supabase Authentication</p>
                <p className="text-sm text-gray-600">Click "Test Supabase Sign In" to verify new auth is working</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium">3</div>
              <div>
                <p className="font-medium">Compare User Data</p>
                <p className="text-sm text-gray-600">Verify both systems return the same user information</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium">4</div>
              <div>
                <p className="font-medium">Test App Functionality</p>
                <p className="text-sm text-gray-600">Navigate through the app to ensure all features work with both auth systems</p>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800">Important Notes</p>
                <ul className="text-yellow-700 mt-1 space-y-1">
                  <li>• Both auth systems are currently active for testing</li>
                  <li>• User data is not automatically synced between systems</li>
                  <li>• Once migration is confirmed, Firebase auth will be removed</li>
                  <li>• Test thoroughly before proceeding to data migration</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthMigration;
